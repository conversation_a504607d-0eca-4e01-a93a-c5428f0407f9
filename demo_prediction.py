#!/usr/bin/env python3
"""
Demo script for large-scale gait parameter prediction
"""

import os
import pandas as pd
import numpy as np
import pickle
import json
from gait_regression_pipeline import GaitRegressionPipeline

def demo_batch_prediction():
    """Demonstrate batch prediction on a small subset."""

    print("=== Demo: Large-Scale Gait Parameter Prediction ===")

    # Initialize pipeline
    pipeline = GaitRegressionPipeline()

    # Load trained models
    print("Loading trained models...")

    try:
        with open('regression_model.pkl', 'rb') as f:
            pipeline.regression_model = pickle.load(f)

        with open('scaler.pkl', 'rb') as f:
            pipeline.scaler = pickle.load(f)

        with open('pca_model.pkl', 'rb') as f:
            pipeline.pca = pickle.load(f)

        with open('parameter_names.json', 'r') as f:
            pipeline.target_params = json.load(f)

        print("Models loaded successfully!")

    except FileNotFoundError as e:
        print(f"Error loading models: {e}")
        print("Please run the main pipeline first to train the models.")
        return

    # Load GAITRite data for reference
    pipeline.load_data()

    # Initialize tokenizer for embedding extraction
    pipeline.initialize_tokenizer()

    # Demo prediction on a small batch (10 videos)
    print("\nRunning demo prediction on 10 videos...")

    # Get available videos (excluding training videos)
    available_videos = [f for f in os.listdir(pipeline.video_dir) if f.endswith('.avi')]

    # Load training videos to exclude
    if os.path.exists('few_shot_valid_videos.json'):
        with open('few_shot_valid_videos.json', 'r') as f:
            training_videos = set(json.load(f))
        available_videos = [v for v in available_videos if v not in training_videos]

    # Select first 10 for demo
    demo_videos = available_videos[:10]

    print(f"Demo videos: {demo_videos}")

    # Extract embeddings and predict
    predictions = []
    video_names = []

    for video_filename in demo_videos:
        video_path = pipeline.video_dir / video_filename

        print(f"Processing: {video_filename}")

        # Extract embedding
        embedding = pipeline.extract_video_embedding(str(video_path))
        if embedding is not None:
            # Predict parameters
            embedding_array = np.array([embedding])  # Add batch dimension
            prediction = pipeline.predict_parameters(embedding_array)

            predictions.append(prediction[0])  # Remove batch dimension
            video_names.append(video_filename)

            print(f"  ✓ Predicted parameters for {video_filename}")
        else:
            print(f"  ✗ Failed to process {video_filename}")

    if len(predictions) > 0:
        # Create results DataFrame
        predictions_df = pd.DataFrame(
            predictions,
            columns=pipeline.target_params,
            index=video_names
        )

        print(f"\nDemo prediction results:")
        print(f"Successfully predicted parameters for {len(predictions)} videos")
        print("\nSample predictions (first 5 parameters):")
        print(predictions_df.iloc[:, :5].round(2))

        # Save demo results
        predictions_df.to_csv('demo_predictions.csv')
        print(f"\nDemo results saved to 'demo_predictions.csv'")

        # Compare with actual values if available
        print("\nComparing with actual GAITRite values (where available):")
        for i, video_name in enumerate(video_names[:3]):  # Show first 3
            # Find corresponding GAITRite data
            gaitrite_match = pipeline.gaitrite_data[
                pipeline.gaitrite_data['video_filename'] == video_name
            ]

            if len(gaitrite_match) > 0:
                actual = gaitrite_match.iloc[0][pipeline.target_params].values
                predicted = predictions[i]

                print(f"\n{video_name}:")
                print("Parameter | Actual | Predicted | Diff")
                print("-" * 40)
                for j, param in enumerate(pipeline.target_params[:5]):  # Show first 5
                    diff = abs(actual[j] - predicted[j])
                    print(f"{param:12} | {actual[j]:6.1f} | {predicted[j]:9.1f} | {diff:4.1f}")
            else:
                print(f"\n{video_name}: No GAITRite data available for comparison")

    else:
        print("No successful predictions made.")

if __name__ == "__main__":
    demo_batch_prediction()