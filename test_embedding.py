#!/usr/bin/env python3
"""
Test script to debug embedding extraction
"""

import os
import numpy as np
import torch
import mediapy as media
from pathlib import Path

# Import Cosmos tokenizer
import importlib
import cosmos_tokenizer.video_lib
importlib.reload(cosmos_tokenizer.video_lib)
from cosmos_tokenizer.video_lib import CausalVideoTokenizer
from cosmos_tokenizer.utils import numpy2tensor, pad_video_batch, tensor2numpy

def test_single_video():
    """Test embedding extraction for a single video."""

    # Initialize tokenizer
    model_name = "Cosmos-1.0-Tokenizer-CV8x8x8"
    encoder_ckpt = f"notebook/pretrained_ckpts/{model_name}/encoder.jit"
    decoder_ckpt = f"notebook/pretrained_ckpts/{model_name}/decoder.jit"

    tokenizer = CausalVideoTokenizer(
        checkpoint_enc=encoder_ckpt,
        checkpoint_dec=decoder_ckpt,
        device="cuda",
        dtype="bfloat16",
    )

    # Test with a single video
    video_path = "data/videos/video_formated_trim/1174017_test_1_trial_2.avi"
    temporal_window = 49

    print(f"Testing video: {video_path}")

    # Read video
    input_video = media.read_video(video_path)[..., :3]
    print(f"Video shape: {input_video.shape}")

    # Add batch dimension
    batched_input_video = np.expand_dims(input_video, axis=0)
    num_frames = batched_input_video.shape[1]
    print(f"Batched video shape: {batched_input_video.shape}")
    print(f"Number of frames: {num_frames}")

    embeddings = []

    # Process video in temporal windows
    for idx in range(0, (num_frames - 1) // temporal_window + 1):
        start, end = idx * temporal_window, (idx + 1) * temporal_window
        input_video_window = batched_input_video[:, start:end, ...]

        print(f"Window {idx}: frames {start}-{end}, shape: {input_video_window.shape}")

        # Pad and convert to tensor
        padded_input_video, crop_region = pad_video_batch(input_video_window)
        print(f"Padded shape: {padded_input_video.shape}")

        input_tensor = numpy2tensor(
            padded_input_video, dtype=tokenizer._dtype, device=tokenizer._device
        )
        print(f"Input tensor shape: {input_tensor.shape}")

        # Extract encoding
        with torch.no_grad():
            encoding = tokenizer.encode(input_tensor)
            print(f"Encoding type: {type(encoding)}")

            # Handle tuple output
            if isinstance(encoding, tuple):
                encoding_tensor = encoding[0]
                print(f"Encoding tuple length: {len(encoding)}")
                print(f"First element shape: {encoding_tensor.shape}")
            else:
                encoding_tensor = encoding
                print(f"Encoding tensor shape: {encoding_tensor.shape}")

            # Convert to float32 and numpy
            encoding_np = encoding_tensor.float().cpu().numpy()
            print(f"Numpy encoding shape: {encoding_np.shape}")

            # Flatten
            flattened = encoding_np.flatten()
            print(f"Flattened shape: {flattened.shape}")

            embeddings.append(flattened)

    print(f"\nTotal embeddings: {len(embeddings)}")
    for i, emb in enumerate(embeddings):
        print(f"Embedding {i} shape: {emb.shape}")

    # Try to combine
    if embeddings:
        try:
            final_embedding = np.mean(embeddings, axis=0)
            print(f"Final embedding shape: {final_embedding.shape}")
        except Exception as e:
            print(f"Error combining embeddings: {e}")
            # Try stacking first
            try:
                stacked = np.stack(embeddings)
                print(f"Stacked shape: {stacked.shape}")
                final_embedding = np.mean(stacked, axis=0)
                print(f"Final embedding shape: {final_embedding.shape}")
            except Exception as e2:
                print(f"Error stacking embeddings: {e2}")

if __name__ == "__main__":
    test_single_video()