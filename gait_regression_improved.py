#!/usr/bin/env python3
"""
Improved Few-Shot Gait Parameter Regression Pipeline
Fixes overfitting and generalization issues
"""

import os
import pandas as pd
import numpy as np
import torch
import mediapy as media
from pathlib import Path
from typing import List, Tuple, Dict, Optional
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.pipeline import Pipeline
import pickle
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer
import importlib
import cosmos_tokenizer.video_lib
importlib.reload(cosmos_tokenizer.video_lib)
from cosmos_tokenizer.video_lib import CausalVideoTokenizer
from cosmos_tokenizer.utils import numpy2tensor, pad_video_batch, tensor2numpy


class ImprovedGaitRegressionPipeline:
    """Improved pipeline with better generalization."""

    def __init__(self,
                 data_dir: str = "data",
                 model_name: str = "Cosmos-1.0-Tokenizer-CV8x8x8",
                 temporal_window: int = 49,
                 device: str = "cuda",
                 dtype: str = "bfloat16"):

        self.data_dir = Path(data_dir)
        self.video_dir = self.data_dir / "videos" / "video_formated_trim"
        self.gaitrite_file = self.data_dir / "gaitrite_full_dataset.xlsx"

        self.model_name = model_name
        self.temporal_window = temporal_window
        self.device = device
        self.dtype = dtype

        # Target parameters (28 total)
        self.target_params = [
            'Velocity', 'Cadence', 'Cycle_Time_L', 'Cycle_Time_R', 'Stride_Len_L', 'Stride_Len_R',
            'StrideVelocity_L', 'StrideVelocity_R', 'Supp_Base_L', 'Supp_Base_R', 'Swing_Perc_L',
            'Swing_Perc_R', 'Stance_Perc_L', 'Stance_Perc_R', 'D_Supp_PercL', 'D_Supp_PercR',
            'ToeInOutL', 'ToeInOutR', 'StrideLen_SD_L', 'StrideLen_SD_R', 'StrideTm_SD_L',
            'StrideTm_SD_R', 'StrideTm_L', 'StrideTm_R', 'StrideLen_CV_L', 'StrideLen_CV_R',
            'StrideTm_CV_L', 'StrideTm_CV_R'
        ]

        # Initialize components
        self.tokenizer = None
        self.pipeline = None  # sklearn Pipeline
        self.gaitrite_data = None

    def load_data(self):
        """Load and analyze GAITRite dataset."""
        print("Loading GAITRite dataset...")
        self.gaitrite_data = pd.read_excel(self.gaitrite_file)

        print(f"Dataset shape: {self.gaitrite_data.shape}")
        print(f"Available target parameters: {len([p for p in self.target_params if p in self.gaitrite_data.columns])}/28")

        # Extract video filename from full path
        self.gaitrite_data['video_filename'] = self.gaitrite_data['VideoFile'].apply(
            lambda x: os.path.basename(x) if pd.notna(x) else None
        )

        return self.gaitrite_data

    def create_larger_training_set(self, n_samples: int = 300) -> List[str]:
        """
        Create a larger, more diverse training set.
        """
        print(f"\nCreating larger training set with {n_samples} samples...")

        # Filter data to only include videos that exist in our directory
        available_videos = set(os.listdir(self.video_dir))
        valid_data = self.gaitrite_data[
            self.gaitrite_data['video_filename'].isin(available_videos)
        ].copy()

        print(f"Found {len(valid_data)} valid video-parameter pairs")

        # Remove rows with missing target parameters
        valid_data = valid_data.dropna(subset=self.target_params)
        print(f"After removing missing parameters: {len(valid_data)} samples")

        if len(valid_data) < n_samples:
            print(f"Warning: Only {len(valid_data)} valid samples available, using all of them")
            return valid_data['video_filename'].tolist()

        # More sophisticated stratified sampling
        selected_indices = []

        # Multiple stratification criteria
        velocity_bins = pd.qcut(valid_data['Velocity'], q=6, labels=False, duplicates='drop')
        stride_bins = pd.qcut(valid_data['Stride_Len_L'], q=4, labels=False, duplicates='drop')
        cadence_bins = pd.qcut(valid_data['Cadence'], q=4, labels=False, duplicates='drop')

        # Create stratification groups
        valid_data['strat_group'] = (
            velocity_bins.astype(str) + '_' +
            stride_bins.astype(str) + '_' +
            cadence_bins.astype(str)
        )

        # Sample from each group
        samples_per_group = max(2, n_samples // valid_data['strat_group'].nunique())

        for group in valid_data['strat_group'].unique():
            group_data = valid_data[valid_data['strat_group'] == group]
            n_group_samples = min(samples_per_group, len(group_data))

            if n_group_samples > 0:
                group_samples = group_data.sample(n=n_group_samples, random_state=42)
                selected_indices.extend(group_samples.index.tolist())

        # Fill remaining slots randomly
        if len(selected_indices) < n_samples:
            remaining_indices = set(valid_data.index) - set(selected_indices)
            additional_needed = n_samples - len(selected_indices)
            additional_samples = np.random.choice(
                list(remaining_indices),
                size=min(additional_needed, len(remaining_indices)),
                replace=False
            )
            selected_indices.extend(additional_samples)

        # Trim to exact number
        selected_indices = selected_indices[:n_samples]
        selected_videos = valid_data.loc[selected_indices, 'video_filename'].tolist()

        print(f"Selected {len(selected_videos)} diverse videos for training")

        return selected_videos

    def initialize_tokenizer(self):
        """Initialize the Cosmos video tokenizer."""
        print(f"\nInitializing Cosmos tokenizer: {self.model_name}")

        encoder_ckpt = f"notebook/pretrained_ckpts/{self.model_name}/encoder.jit"
        decoder_ckpt = f"notebook/pretrained_ckpts/{self.model_name}/decoder.jit"

        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")

        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            checkpoint_dec=decoder_ckpt,
            device=self.device,
            dtype=self.dtype,
        )

        print("Tokenizer initialized successfully")

    def extract_video_embedding(self, video_path: str) -> Optional[np.ndarray]:
        """Extract embedding from a single video using Cosmos tokenizer."""
        try:
            # Read video
            input_video = media.read_video(video_path)[..., :3]
            if input_video.ndim != 4 or input_video.shape[-1] != 3:
                print(f"Invalid video format: {video_path}")
                return None

            # Add batch dimension
            batched_input_video = np.expand_dims(input_video, axis=0)
            num_frames = batched_input_video.shape[1]

            embeddings = []

            # Process video in temporal windows - only use complete windows
            num_complete_windows = num_frames // self.temporal_window

            for idx in range(num_complete_windows):
                start, end = idx * self.temporal_window, (idx + 1) * self.temporal_window
                input_video_window = batched_input_video[:, start:end, ...]

                # Pad and convert to tensor
                padded_input_video, crop_region = pad_video_batch(input_video_window)
                input_tensor = numpy2tensor(
                    padded_input_video, dtype=self.tokenizer._dtype, device=self.tokenizer._device
                )

                # Extract encoding
                with torch.no_grad():
                    encoding = self.tokenizer.encode(input_tensor)
                    # Handle tuple output
                    if isinstance(encoding, tuple):
                        encoding_tensor = encoding[0]
                    else:
                        encoding_tensor = encoding
                    # Convert to float32 and flatten
                    encoding_np = encoding_tensor.float().cpu().numpy()
                    embeddings.append(encoding_np.flatten())

            # If no complete windows, process the entire video as one window
            if len(embeddings) == 0 and num_frames > 0:
                padded_input_video, crop_region = pad_video_batch(batched_input_video)
                input_tensor = numpy2tensor(
                    padded_input_video, dtype=self.tokenizer._dtype, device=self.tokenizer._device
                )

                with torch.no_grad():
                    encoding = self.tokenizer.encode(input_tensor)
                    if isinstance(encoding, tuple):
                        encoding_tensor = encoding[0]
                    else:
                        encoding_tensor = encoding
                    encoding_np = encoding_tensor.float().cpu().numpy()
                    embeddings.append(encoding_np.flatten())

            # Combine embeddings from all windows (average pooling)
            if embeddings:
                final_embedding = np.mean(embeddings, axis=0)
                return final_embedding
            else:
                return None

        except Exception as e:
            print(f"Error processing video {video_path}: {str(e)}")
            return None

    def extract_training_embeddings(self, video_filenames: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """Extract embeddings for training videos."""
        print(f"\nExtracting embeddings for {len(video_filenames)} training videos...")

        if self.tokenizer is None:
            self.initialize_tokenizer()

        embeddings = []
        parameters = []
        valid_videos = []

        for i, video_filename in enumerate(tqdm(video_filenames, desc="Processing videos")):
            video_path = self.video_dir / video_filename

            if not video_path.exists():
                print(f"Video not found: {video_path}")
                continue

            # Extract embedding
            embedding = self.extract_video_embedding(str(video_path))
            if embedding is None:
                print(f"Failed to extract embedding for: {video_filename}")
                continue

            # Get corresponding parameters
            video_data = self.gaitrite_data[
                self.gaitrite_data['video_filename'] == video_filename
            ]

            if len(video_data) == 0:
                print(f"No GAITRite data found for: {video_filename}")
                continue

            # Use the first matching row if multiple exist
            params = video_data.iloc[0][self.target_params].values

            # Check for missing parameters
            if np.any(pd.isna(params)):
                print(f"Missing parameters for: {video_filename}")
                continue

            embeddings.append(embedding)
            parameters.append(params)
            valid_videos.append(video_filename)

        if len(embeddings) == 0:
            raise ValueError("No valid embeddings extracted!")

        embeddings_array = np.array(embeddings)
        parameters_array = np.array(parameters)

        print(f"\nSuccessfully extracted embeddings for {len(embeddings)} videos")
        print(f"Embedding shape: {embeddings_array.shape}")
        print(f"Parameters shape: {parameters_array.shape}")

        # Save the data
        np.save('improved_embeddings.npy', embeddings_array)
        np.save('improved_parameters.npy', parameters_array)
        with open('improved_valid_videos.json', 'w') as f:
            json.dump(valid_videos, f, indent=2)

        return embeddings_array, parameters_array

    def train_improved_model(self, embeddings: np.ndarray, parameters: np.ndarray) -> Dict:
        """
        Train improved regression model with better generalization.
        """
        print(f"\nTraining improved regression model...")
        print(f"Input shape: {embeddings.shape}")
        print(f"Target shape: {parameters.shape}")

        # 1. Use more conservative PCA to prevent overfitting
        # Keep only components that explain significant variance
        n_samples, n_features = embeddings.shape

        # Conservative PCA: keep at most 50% of samples or 100 components, whichever is smaller
        max_components = min(n_samples // 2, 100, n_features)
        print(f"Using conservative PCA: {n_features} -> {max_components} components")

        # 2. Create sklearn Pipeline for proper preprocessing
        models_to_test = {
            'Ridge_weak': Pipeline([
                ('scaler', RobustScaler()),
                ('pca', PCA(n_components=max_components, random_state=42)),
                ('regressor', Ridge(alpha=10.0, random_state=42))  # Stronger regularization
            ]),
            'Ridge_medium': Pipeline([
                ('scaler', RobustScaler()),
                ('pca', PCA(n_components=max_components, random_state=42)),
                ('regressor', Ridge(alpha=50.0, random_state=42))
            ]),
            'Ridge_strong': Pipeline([
                ('scaler', RobustScaler()),
                ('pca', PCA(n_components=max_components, random_state=42)),
                ('regressor', Ridge(alpha=100.0, random_state=42))
            ]),
            'ElasticNet': Pipeline([
                ('scaler', RobustScaler()),
                ('pca', PCA(n_components=max_components, random_state=42)),
                ('regressor', ElasticNet(alpha=1.0, l1_ratio=0.5, random_state=42, max_iter=2000))
            ])
        }

        # 3. Use proper train/validation split (30% for validation)
        X_train, X_val, y_train, y_val = train_test_split(
            embeddings, parameters, test_size=0.3, random_state=42, shuffle=True
        )

        print(f"Train set: {X_train.shape}, Validation set: {X_val.shape}")

        # 4. Cross-validation for model selection
        results = {}
        best_model = None
        best_score = -np.inf

        for name, model in models_to_test.items():
            print(f"\nEvaluating {name}...")

            try:
                # Cross-validation on training set
                cv_scores = cross_val_score(
                    model, X_train, y_train,
                    cv=KFold(n_splits=5, shuffle=True, random_state=42),
                    scoring='r2',
                    n_jobs=-1
                )

                cv_mean = cv_scores.mean()
                cv_std = cv_scores.std()

                print(f"{name} - CV R²: {cv_mean:.3f} ± {cv_std:.3f}")

                # Train on full training set and evaluate on validation
                model.fit(X_train, y_train)

                # Training metrics
                train_pred = model.predict(X_train)
                train_r2 = r2_score(y_train, train_pred, multioutput='uniform_average')
                train_mae = mean_absolute_error(y_train, train_pred, multioutput='uniform_average')

                # Validation metrics
                val_pred = model.predict(X_val)
                val_r2 = r2_score(y_val, val_pred, multioutput='uniform_average')
                val_mae = mean_absolute_error(y_val, val_pred, multioutput='uniform_average')

                print(f"{name} - Train R²: {train_r2:.3f}, Train MAE: {train_mae:.3f}")
                print(f"{name} - Val R²: {val_r2:.3f}, Val MAE: {val_mae:.3f}")

                results[name] = {
                    'model': model,
                    'cv_mean': cv_mean,
                    'cv_std': cv_std,
                    'train_r2': train_r2,
                    'train_mae': train_mae,
                    'val_r2': val_r2,
                    'val_mae': val_mae,
                    'score': val_r2  # Use validation R² for model selection
                }

                # Track best model based on validation performance
                if val_r2 > best_score:
                    best_score = val_r2
                    best_model = name

            except Exception as e:
                print(f"Error training {name}: {str(e)}")
                continue

        if best_model is None:
            raise ValueError("No models trained successfully!")

        print(f"\n" + "="*50)
        print(f"BEST MODEL: {best_model}")
        print(f"Validation R²: {best_score:.3f}")
        print(f"Cross-validation R²: {results[best_model]['cv_mean']:.3f} ± {results[best_model]['cv_std']:.3f}")
        print("="*50)

        # Save the best model
        self.pipeline = results[best_model]['model']

        # Save model and results
        with open('improved_model.pkl', 'wb') as f:
            pickle.dump(self.pipeline, f)

        with open('improved_results.json', 'w') as f:
            # Convert numpy types to Python types for JSON serialization
            json_results = {}
            for name, result in results.items():
                json_results[name] = {
                    'cv_mean': float(result['cv_mean']),
                    'cv_std': float(result['cv_std']),
                    'train_r2': float(result['train_r2']),
                    'train_mae': float(result['train_mae']),
                    'val_r2': float(result['val_r2']),
                    'val_mae': float(result['val_mae']),
                    'score': float(result['score'])
                }
            json.dump(json_results, f, indent=2)

        return results

    def predict_parameters(self, embeddings: np.ndarray) -> np.ndarray:
        """Predict GAITRite parameters from embeddings using trained pipeline."""
        if self.pipeline is None:
            raise ValueError("No trained model available!")

        # Pipeline handles all preprocessing automatically
        predictions = self.pipeline.predict(embeddings)
        return predictions


def main():
    """Main execution function for improved pipeline."""
    print("=== IMPROVED Few-Shot Gait Parameter Regression Pipeline ===")
    print("Addressing overfitting and generalization issues")

    # Initialize improved pipeline
    pipeline = ImprovedGaitRegressionPipeline()

    # Load and analyze data
    pipeline.load_data()

    # Create larger, more diverse training set
    training_videos = pipeline.create_larger_training_set(n_samples=200)  # Increased sample size

    print(f"\nTraining dataset created with {len(training_videos)} videos")
    print("Sample videos:", training_videos[:5])

    # Save training video list
    with open('improved_training_videos.json', 'w') as f:
        json.dump(training_videos, f, indent=2)

    print("\n" + "="*60)
    print("STEP 1: Data preparation completed successfully!")

    # Extract embeddings for training videos
    print("\n" + "="*60)
    print("STEP 2: Extracting video embeddings...")

    try:
        embeddings, parameters = pipeline.extract_training_embeddings(training_videos)

        print(f"\nEmbedding extraction completed!")
        print(f"Embedding shape: {embeddings.shape}")
        print(f"Parameters shape: {parameters.shape}")

        print("\n" + "="*60)
        print("STEP 2: Video embedding extraction completed successfully!")

        # Train improved regression model
        print("\n" + "="*60)
        print("STEP 3: Training improved regression model...")

        try:
            training_results = pipeline.train_improved_model(embeddings, parameters)

            print(f"\nImproved model training completed!")
            print("\nTraining results summary:")
            for model_name, results in training_results.items():
                print(f"  {model_name}:")
                print(f"    CV R²: {results['cv_mean']:.3f} ± {results['cv_std']:.3f}")
                print(f"    Val R²: {results['val_r2']:.3f}")
                print(f"    Val MAE: {results['val_mae']:.3f}")

            print("\n" + "="*60)
            print("STEP 3: Improved model training completed successfully!")

            # Quick validation test
            print("\n" + "="*60)
            print("STEP 4: Quick validation test...")

            # Test on a few videos not in training set
            all_videos = [f for f in os.listdir(pipeline.video_dir) if f.endswith('.avi')]
            training_set = set(training_videos)
            test_videos = [v for v in all_videos if v not in training_set][:5]

            print(f"Testing on {len(test_videos)} videos not in training set...")

            test_predictions = []
            test_actuals = []

            for video_filename in test_videos:
                video_path = pipeline.video_dir / video_filename

                # Extract embedding
                embedding = pipeline.extract_video_embedding(str(video_path))
                if embedding is not None:
                    # Predict parameters
                    embedding_array = np.array([embedding])
                    prediction = pipeline.predict_parameters(embedding_array)

                    # Get actual parameters if available
                    video_data = pipeline.gaitrite_data[
                        pipeline.gaitrite_data['video_filename'] == video_filename
                    ]

                    if len(video_data) > 0:
                        actual = video_data.iloc[0][pipeline.target_params].values
                        if not np.any(pd.isna(actual)):
                            test_predictions.append(prediction[0])
                            test_actuals.append(actual)
                            print(f"✓ Processed {video_filename}")

            if len(test_predictions) > 0:
                test_predictions = np.array(test_predictions)
                test_actuals = np.array(test_actuals)

                test_r2 = r2_score(test_actuals, test_predictions, multioutput='uniform_average')
                test_mae = mean_absolute_error(test_actuals, test_predictions, multioutput='uniform_average')

                print(f"\nTest set performance:")
                print(f"  Test R²: {test_r2:.3f}")
                print(f"  Test MAE: {test_mae:.3f}")

                # Save test results
                test_results_df = pd.DataFrame(
                    test_predictions,
                    columns=pipeline.target_params
                )
                test_results_df.to_csv('improved_test_predictions.csv')

                print("\n" + "="*60)
                print("IMPROVED PIPELINE COMPLETED SUCCESSFULLY!")
                print("="*60)
                print(f"Key improvements:")
                print(f"- Larger training set: {len(training_videos)} samples")
                print(f"- Conservative PCA: prevents overfitting")
                print(f"- Stronger regularization: α=10-100")
                print(f"- Cross-validation: robust model selection")
                print(f"- Proper validation: 30% holdout")
                print(f"- Test performance: R²={test_r2:.3f}, MAE={test_mae:.3f}")

        except Exception as e:
            print(f"Error during model training: {str(e)}")
            print("Please check the error and try again.")

    except Exception as e:
        print(f"Error during embedding extraction: {str(e)}")
        print("Please check the error and try again.")


if __name__ == "__main__":
    main()