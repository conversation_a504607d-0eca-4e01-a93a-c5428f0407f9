#!/usr/bin/env python3
"""
Few-Shot Gait Parameter Regression Pipeline using Cosmos Video Tokenizer

This script implements a complete pipeline for predicting GAITRite parameters from video embeddings
using a few-shot learning approach with the Cosmos video tokenizer.
"""

import os
import pandas as pd
import numpy as np
import torch
import cv2
import mediapy as media
from pathlib import Path
from typing import List, Tuple, Dict, Optional
from sklearn.model_selection import train_test_split
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import mean_squared_error, r2_score
import pickle
import json
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# Import Cosmos tokenizer
import importlib
import cosmos_tokenizer.video_lib
importlib.reload(cosmos_tokenizer.video_lib)
from cosmos_tokenizer.video_lib import CausalVideoTokenizer
from cosmos_tokenizer.utils import numpy2tensor, pad_video_batch, tensor2numpy


class GaitRegressionPipeline:
    """Complete pipeline for gait parameter regression using Cosmos tokenizer."""

    def __init__(self,
                 data_dir: str = "data",
                 model_name: str = "Cosmos-1.0-Tokenizer-CV8x8x8",
                 temporal_window: int = 49,
                 device: str = "cuda",
                 dtype: str = "bfloat16"):
        """
        Initialize the pipeline.

        Args:
            data_dir: Directory containing videos and GAITRite data
            model_name: Cosmos tokenizer model name
            temporal_window: Temporal window for video processing
            device: Device for computation (cuda/cpu)
            dtype: Data type for computation
        """
        self.data_dir = Path(data_dir)
        self.video_dir = self.data_dir / "videos" / "video_formated_trim"
        self.gaitrite_file = self.data_dir / "gaitrite_full_dataset.xlsx"

        self.model_name = model_name
        self.temporal_window = temporal_window
        self.device = device
        self.dtype = dtype

        # Target parameters (28 total)
        self.target_params = [
            'Velocity', 'Cadence', 'Cycle_Time_L', 'Cycle_Time_R', 'Stride_Len_L', 'Stride_Len_R',
            'StrideVelocity_L', 'StrideVelocity_R', 'Supp_Base_L', 'Supp_Base_R', 'Swing_Perc_L',
            'Swing_Perc_R', 'Stance_Perc_L', 'Stance_Perc_R', 'D_Supp_PercL', 'D_Supp_PercR',
            'ToeInOutL', 'ToeInOutR', 'StrideLen_SD_L', 'StrideLen_SD_R', 'StrideTm_SD_L',
            'StrideTm_SD_R', 'StrideTm_L', 'StrideTm_R', 'StrideLen_CV_L', 'StrideLen_CV_R',
            'StrideTm_CV_L', 'StrideTm_CV_R'
        ]

        # Initialize components
        self.tokenizer = None
        self.scaler = StandardScaler()
        self.pca = None
        self.regression_model = None
        self.gaitrite_data = None

    def load_data(self):
        """Load and analyze GAITRite dataset."""
        print("Loading GAITRite dataset...")
        self.gaitrite_data = pd.read_excel(self.gaitrite_file)

        print(f"Dataset shape: {self.gaitrite_data.shape}")
        print(f"Available target parameters: {len([p for p in self.target_params if p in self.gaitrite_data.columns])}/28")

        # Extract video filename from full path
        self.gaitrite_data['video_filename'] = self.gaitrite_data['VideoFile'].apply(
            lambda x: os.path.basename(x) if pd.notna(x) else None
        )

        return self.gaitrite_data

    def analyze_data_distribution(self):
        """Analyze the distribution of gait parameters for diverse sampling."""
        print("\nAnalyzing parameter distributions for diverse sampling...")

        # Key parameters for diversity analysis
        diversity_params = ['Velocity', 'Stride_Len_L', 'Stride_Len_R', 'Cadence', 'ToeInOutL', 'ToeInOutR']

        param_stats = {}
        for param in diversity_params:
            if param in self.gaitrite_data.columns:
                data = self.gaitrite_data[param].dropna()
                param_stats[param] = {
                    'mean': data.mean(),
                    'std': data.std(),
                    'min': data.min(),
                    'max': data.max(),
                    'q25': data.quantile(0.25),
                    'q75': data.quantile(0.75)
                }
                print(f"{param}: mean={data.mean():.2f}, std={data.std():.2f}, range=[{data.min():.2f}, {data.max():.2f}]")

        return param_stats

    def create_diverse_few_shot_dataset(self, n_samples: int = 150) -> List[str]:
        """
        Create a diverse few-shot dataset by selecting videos with varied gait characteristics.

        Args:
            n_samples: Number of samples to select for few-shot training

        Returns:
            List of selected video filenames
        """
        print(f"\nCreating diverse few-shot dataset with {n_samples} samples...")

        # Filter data to only include videos that exist in our directory
        available_videos = set(os.listdir(self.video_dir))
        valid_data = self.gaitrite_data[
            self.gaitrite_data['video_filename'].isin(available_videos)
        ].copy()

        print(f"Found {len(valid_data)} valid video-parameter pairs")

        # Remove rows with missing target parameters
        valid_data = valid_data.dropna(subset=self.target_params)
        print(f"After removing missing parameters: {len(valid_data)} samples")

        if len(valid_data) < n_samples:
            print(f"Warning: Only {len(valid_data)} valid samples available, using all of them")
            return valid_data['video_filename'].tolist()

        # Stratified sampling based on key parameters
        selected_indices = []

        # Define bins for stratification
        velocity_bins = pd.qcut(valid_data['Velocity'], q=5, labels=False, duplicates='drop')
        stride_bins = pd.qcut(valid_data['Stride_Len_L'], q=3, labels=False, duplicates='drop')
        cadence_bins = pd.qcut(valid_data['Cadence'], q=3, labels=False, duplicates='drop')

        # Create stratification groups
        valid_data['strat_group'] = (
            velocity_bins.astype(str) + '_' +
            stride_bins.astype(str) + '_' +
            cadence_bins.astype(str)
        )

        # Sample from each group
        samples_per_group = max(1, n_samples // valid_data['strat_group'].nunique())

        for group in valid_data['strat_group'].unique():
            group_data = valid_data[valid_data['strat_group'] == group]
            n_group_samples = min(samples_per_group, len(group_data))

            if n_group_samples > 0:
                group_samples = group_data.sample(n=n_group_samples, random_state=42)
                selected_indices.extend(group_samples.index.tolist())

        # If we need more samples, randomly select from remaining
        if len(selected_indices) < n_samples:
            remaining_indices = set(valid_data.index) - set(selected_indices)
            additional_needed = n_samples - len(selected_indices)
            additional_samples = np.random.choice(
                list(remaining_indices),
                size=min(additional_needed, len(remaining_indices)),
                replace=False
            )
            selected_indices.extend(additional_samples)

        # Trim to exact number if we have too many
        selected_indices = selected_indices[:n_samples]

        selected_videos = valid_data.loc[selected_indices, 'video_filename'].tolist()

        print(f"Selected {len(selected_videos)} diverse videos for few-shot training")

        # Print diversity statistics
        selected_data = valid_data.loc[selected_indices]
        print("\nDiversity statistics for selected samples:")
        for param in ['Velocity', 'Stride_Len_L', 'Cadence']:
            if param in selected_data.columns:
                data = selected_data[param]
                print(f"{param}: range=[{data.min():.2f}, {data.max():.2f}], std={data.std():.2f}")

        return selected_videos

    def initialize_tokenizer(self):
        """Initialize the Cosmos video tokenizer."""
        print(f"\nInitializing Cosmos tokenizer: {self.model_name}")

        encoder_ckpt = f"notebook/pretrained_ckpts/{self.model_name}/encoder.jit"
        decoder_ckpt = f"notebook/pretrained_ckpts/{self.model_name}/decoder.jit"

        if not os.path.exists(encoder_ckpt):
            raise FileNotFoundError(f"Encoder checkpoint not found: {encoder_ckpt}")

        self.tokenizer = CausalVideoTokenizer(
            checkpoint_enc=encoder_ckpt,
            checkpoint_dec=decoder_ckpt,
            device=self.device,
            dtype=self.dtype,
        )

        print("Tokenizer initialized successfully")

    def extract_video_embedding(self, video_path: str) -> Optional[np.ndarray]:
        """
        Extract embedding from a single video using Cosmos tokenizer.

        Args:
            video_path: Path to video file

        Returns:
            Flattened embedding vector or None if extraction fails
        """
        try:
            # Read video
            input_video = media.read_video(video_path)[..., :3]
            if input_video.ndim != 4 or input_video.shape[-1] != 3:
                print(f"Invalid video format: {video_path}")
                return None

            # Add batch dimension
            batched_input_video = np.expand_dims(input_video, axis=0)
            num_frames = batched_input_video.shape[1]

            embeddings = []

            # Process video in temporal windows - only use complete windows
            num_complete_windows = num_frames // self.temporal_window

            for idx in range(num_complete_windows):
                start, end = idx * self.temporal_window, (idx + 1) * self.temporal_window
                input_video_window = batched_input_video[:, start:end, ...]

                # Pad and convert to tensor
                padded_input_video, crop_region = pad_video_batch(input_video_window)
                input_tensor = numpy2tensor(
                    padded_input_video, dtype=self.tokenizer._dtype, device=self.tokenizer._device
                )

                # Extract encoding
                with torch.no_grad():
                    encoding = self.tokenizer.encode(input_tensor)
                    # Handle tuple output (some tokenizers return tuple)
                    if isinstance(encoding, tuple):
                        encoding_tensor = encoding[0]  # Take the first element
                    else:
                        encoding_tensor = encoding
                    # Convert to float32 to avoid BFloat16 issues, then to numpy and flatten
                    encoding_np = encoding_tensor.float().cpu().numpy()
                    embeddings.append(encoding_np.flatten())

            # If no complete windows, process the entire video as one window
            if len(embeddings) == 0 and num_frames > 0:
                # Use the entire video, padded to temporal_window size
                padded_input_video, crop_region = pad_video_batch(batched_input_video)
                input_tensor = numpy2tensor(
                    padded_input_video, dtype=self.tokenizer._dtype, device=self.tokenizer._device
                )

                with torch.no_grad():
                    encoding = self.tokenizer.encode(input_tensor)
                    if isinstance(encoding, tuple):
                        encoding_tensor = encoding[0]
                    else:
                        encoding_tensor = encoding
                    encoding_np = encoding_tensor.float().cpu().numpy()
                    embeddings.append(encoding_np.flatten())

            # Combine embeddings from all windows (average pooling)
            if embeddings:
                # All embeddings should now have the same shape
                final_embedding = np.mean(embeddings, axis=0)
                return final_embedding
            else:
                return None

        except Exception as e:
            print(f"Error processing video {video_path}: {str(e)}")
            return None

    def extract_few_shot_embeddings(self, video_filenames: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """
        Extract embeddings for few-shot videos and corresponding GAITRite parameters.

        Args:
            video_filenames: List of video filenames for few-shot training

        Returns:
            Tuple of (embeddings, parameters) arrays
        """
        print(f"\nExtracting embeddings for {len(video_filenames)} few-shot videos...")

        if self.tokenizer is None:
            self.initialize_tokenizer()

        embeddings = []
        parameters = []
        valid_videos = []

        for i, video_filename in enumerate(tqdm(video_filenames, desc="Processing videos")):
            video_path = self.video_dir / video_filename

            if not video_path.exists():
                print(f"Video not found: {video_path}")
                continue

            # Extract embedding
            embedding = self.extract_video_embedding(str(video_path))
            if embedding is None:
                print(f"Failed to extract embedding for: {video_filename}")
                continue

            # Get corresponding parameters
            video_data = self.gaitrite_data[
                self.gaitrite_data['video_filename'] == video_filename
            ]

            if len(video_data) == 0:
                print(f"No GAITRite data found for: {video_filename}")
                continue

            # Use the first matching row if multiple exist
            params = video_data.iloc[0][self.target_params].values

            # Check for missing parameters
            if np.any(pd.isna(params)):
                print(f"Missing parameters for: {video_filename}")
                continue

            embeddings.append(embedding)
            parameters.append(params)
            valid_videos.append(video_filename)

            if (i + 1) % 10 == 0:
                print(f"Processed {i + 1}/{len(video_filenames)} videos")

        if len(embeddings) == 0:
            raise ValueError("No valid embeddings extracted!")

        embeddings_array = np.array(embeddings)
        parameters_array = np.array(parameters)

        print(f"\nSuccessfully extracted embeddings for {len(embeddings)} videos")
        print(f"Embedding shape: {embeddings_array.shape}")
        print(f"Parameters shape: {parameters_array.shape}")

        # Save the data
        np.save('few_shot_embeddings.npy', embeddings_array)
        np.save('few_shot_parameters.npy', parameters_array)
        with open('few_shot_valid_videos.json', 'w') as f:
            json.dump(valid_videos, f, indent=2)

        return embeddings_array, parameters_array

    def apply_dimensionality_reduction(self, embeddings: np.ndarray, n_components: int = 512) -> np.ndarray:
        """
        Apply PCA dimensionality reduction to embeddings.

        Args:
            embeddings: High-dimensional embeddings
            n_components: Number of PCA components to keep

        Returns:
            Reduced embeddings
        """
        # Adjust n_components based on available samples and features
        max_components = min(embeddings.shape[0] - 1, embeddings.shape[1], n_components)

        print(f"\nApplying PCA dimensionality reduction: {embeddings.shape[1]} -> {max_components}")
        print(f"(Adjusted from requested {n_components} due to sample size: {embeddings.shape[0]})")

        self.pca = PCA(n_components=max_components, random_state=42)
        reduced_embeddings = self.pca.fit_transform(embeddings)

        explained_variance = np.sum(self.pca.explained_variance_ratio_)
        print(f"Explained variance ratio: {explained_variance:.3f}")

        # Save PCA model
        with open('pca_model.pkl', 'wb') as f:
            pickle.dump(self.pca, f)

        return reduced_embeddings

    def train_regression_model(self, embeddings: np.ndarray, parameters: np.ndarray) -> Dict:
        """
        Train regression model to map embeddings to GAITRite parameters.

        Args:
            embeddings: Reduced video embeddings
            parameters: GAITRite parameters

        Returns:
            Dictionary with training results and metrics
        """
        print(f"\nTraining regression model...")
        print(f"Input shape: {embeddings.shape}")
        print(f"Target shape: {parameters.shape}")

        # Standardize the embeddings
        embeddings_scaled = self.scaler.fit_transform(embeddings)

        # Split data for validation (if we have enough samples)
        if len(embeddings) >= 8:
            X_train, X_val, y_train, y_val = train_test_split(
                embeddings_scaled, parameters, test_size=0.2, random_state=42
            )
            print(f"Train set: {X_train.shape}, Validation set: {X_val.shape}")
        else:
            # Use all data for training (few-shot scenario)
            X_train, y_train = embeddings_scaled, parameters
            X_val, y_val = None, None
            print("Using all data for training (few-shot scenario)")

        # Try different regression models
        models = {
            'Ridge': Ridge(alpha=1.0, random_state=42),
            'Lasso': Lasso(alpha=0.1, random_state=42, max_iter=2000),
            'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42, max_iter=2000)
        }

        results = {}
        best_model = None
        best_score = -np.inf

        for name, model in models.items():
            print(f"\nTraining {name}...")

            try:
                # Train the model
                model.fit(X_train, y_train)

                # Evaluate on training set
                train_pred = model.predict(X_train)
                train_r2 = r2_score(y_train, train_pred, multioutput='uniform_average')
                train_mse = mean_squared_error(y_train, train_pred, multioutput='uniform_average')

                print(f"{name} - Train R²: {train_r2:.3f}, Train MSE: {train_mse:.3f}")

                # Evaluate on validation set if available
                if X_val is not None:
                    val_pred = model.predict(X_val)
                    val_r2 = r2_score(y_val, val_pred, multioutput='uniform_average')
                    val_mse = mean_squared_error(y_val, val_pred, multioutput='uniform_average')
                    print(f"{name} - Val R²: {val_r2:.3f}, Val MSE: {val_mse:.3f}")
                    score = val_r2
                else:
                    val_r2, val_mse = None, None
                    score = train_r2

                results[name] = {
                    'model': model,
                    'train_r2': train_r2,
                    'train_mse': train_mse,
                    'val_r2': val_r2,
                    'val_mse': val_mse,
                    'score': score
                }

                # Track best model
                if score > best_score:
                    best_score = score
                    best_model = name

            except Exception as e:
                print(f"Error training {name}: {str(e)}")
                continue

        if best_model is None:
            raise ValueError("No models trained successfully!")

        print(f"\nBest model: {best_model} (Score: {best_score:.3f})")

        # Save the best model
        self.regression_model = results[best_model]['model']

        # Save models and scaler
        with open('regression_model.pkl', 'wb') as f:
            pickle.dump(self.regression_model, f)

        with open('scaler.pkl', 'wb') as f:
            pickle.dump(self.scaler, f)

        # Save parameter names for reference
        with open('parameter_names.json', 'w') as f:
            json.dump(self.target_params, f, indent=2)

        return results

    def predict_parameters(self, embeddings: np.ndarray) -> np.ndarray:
        """
        Predict GAITRite parameters from embeddings using trained model.

        Args:
            embeddings: Video embeddings

        Returns:
            Predicted parameters
        """
        if self.regression_model is None:
            raise ValueError("No trained model available!")

        # Apply same preprocessing
        if self.pca is not None:
            embeddings_reduced = self.pca.transform(embeddings)
        else:
            embeddings_reduced = embeddings

        embeddings_scaled = self.scaler.transform(embeddings_reduced)

        # Predict
        predictions = self.regression_model.predict(embeddings_scaled)

        return predictions

    def batch_predict_all_videos(self, batch_size: int = 50) -> Dict:
        """
        Apply trained model to predict parameters for all available videos.

        Args:
            batch_size: Number of videos to process in each batch

        Returns:
            Dictionary with predictions and metadata
        """
        print(f"\nStarting batch prediction for all videos...")

        if self.regression_model is None:
            raise ValueError("No trained model available! Train model first.")

        # Get all available videos
        available_videos = [f for f in os.listdir(self.video_dir) if f.endswith('.avi')]
        print(f"Found {len(available_videos)} videos to process")

        # Filter out videos used in training to avoid data leakage
        if os.path.exists('few_shot_valid_videos.json'):
            with open('few_shot_valid_videos.json', 'r') as f:
                training_videos = set(json.load(f))
            available_videos = [v for v in available_videos if v not in training_videos]
            print(f"After removing training videos: {len(available_videos)} videos")

        predictions = []
        video_names = []
        failed_videos = []

        # Process in batches
        for i in range(0, len(available_videos), batch_size):
            batch_videos = available_videos[i:i+batch_size]
            print(f"\nProcessing batch {i//batch_size + 1}/{(len(available_videos)-1)//batch_size + 1}")
            print(f"Videos {i+1}-{min(i+batch_size, len(available_videos))} of {len(available_videos)}")

            batch_embeddings = []
            batch_names = []

            for video_filename in tqdm(batch_videos, desc="Extracting embeddings"):
                video_path = self.video_dir / video_filename

                # Extract embedding
                embedding = self.extract_video_embedding(str(video_path))
                if embedding is not None:
                    batch_embeddings.append(embedding)
                    batch_names.append(video_filename)
                else:
                    failed_videos.append(video_filename)

            if len(batch_embeddings) > 0:
                # Convert to array and apply same preprocessing as training
                batch_embeddings = np.array(batch_embeddings)

                # Apply PCA if available
                if self.pca is not None:
                    batch_embeddings_reduced = self.pca.transform(batch_embeddings)
                else:
                    batch_embeddings_reduced = batch_embeddings

                # Predict parameters
                batch_predictions = self.predict_parameters(batch_embeddings)

                predictions.extend(batch_predictions)
                video_names.extend(batch_names)

                print(f"Successfully processed {len(batch_embeddings)} videos in this batch")

            # Save intermediate results
            if len(predictions) > 0:
                intermediate_results = {
                    'video_names': video_names,
                    'predictions': np.array(predictions),
                    'parameter_names': self.target_params,
                    'failed_videos': failed_videos,
                    'processed_count': len(predictions),
                    'total_count': len(available_videos)
                }

                with open(f'batch_predictions_intermediate.pkl', 'wb') as f:
                    pickle.dump(intermediate_results, f)

        # Final results
        results = {
            'video_names': video_names,
            'predictions': np.array(predictions) if predictions else np.array([]),
            'parameter_names': self.target_params,
            'failed_videos': failed_videos,
            'success_rate': len(predictions) / len(available_videos) if available_videos else 0,
            'total_processed': len(predictions),
            'total_available': len(available_videos)
        }

        # Save final results
        with open('final_predictions.pkl', 'wb') as f:
            pickle.dump(results, f)

        # Save as CSV for easy analysis
        if len(predictions) > 0:
            predictions_df = pd.DataFrame(
                predictions,
                columns=self.target_params,
                index=video_names
            )
            predictions_df.to_csv('predicted_gait_parameters.csv')

        print(f"\nBatch prediction completed!")
        print(f"Successfully processed: {len(predictions)}/{len(available_videos)} videos")
        print(f"Success rate: {results['success_rate']:.1%}")
        print(f"Failed videos: {len(failed_videos)}")

        return results


def main():
    """Main execution function."""
    print("=== Few-Shot Gait Parameter Regression Pipeline ===")

    # Initialize pipeline
    pipeline = GaitRegressionPipeline()

    # Load and analyze data
    pipeline.load_data()
    pipeline.analyze_data_distribution()

    # Create diverse few-shot dataset (use larger sample for better training)
    few_shot_videos = pipeline.create_diverse_few_shot_dataset(n_samples=100)

    print(f"\nFew-shot dataset created with {len(few_shot_videos)} videos")
    print("Sample videos:", few_shot_videos[:5])

    # Save few-shot video list
    with open('few_shot_videos.json', 'w') as f:
        json.dump(few_shot_videos, f, indent=2)

    print("\n" + "="*60)
    print("STEP 1: Data preparation completed successfully!")

    # Extract embeddings for few-shot videos
    print("\n" + "="*60)
    print("STEP 2: Extracting video embeddings...")

    try:
        embeddings, parameters = pipeline.extract_few_shot_embeddings(few_shot_videos)

        # Apply dimensionality reduction
        reduced_embeddings = pipeline.apply_dimensionality_reduction(embeddings, n_components=512)

        print(f"\nEmbedding extraction completed!")
        print(f"Original embedding shape: {embeddings.shape}")
        print(f"Reduced embedding shape: {reduced_embeddings.shape}")
        print(f"Parameters shape: {parameters.shape}")

        # Save reduced embeddings
        np.save('few_shot_embeddings_reduced.npy', reduced_embeddings)

        print("\n" + "="*60)
        print("STEP 2: Video embedding extraction completed successfully!")

        # Train regression model
        print("\n" + "="*60)
        print("STEP 3: Training regression model...")

        try:
            training_results = pipeline.train_regression_model(reduced_embeddings, parameters)

            print(f"\nModel training completed!")
            print("Training results summary:")
            for model_name, results in training_results.items():
                print(f"  {model_name}: R² = {results['score']:.3f}")

            print("\n" + "="*60)
            print("STEP 3: Regression model training completed successfully!")
            print("\nNext steps:")
            print("4. Apply to full dataset for large-scale prediction")

        except Exception as e:
            print(f"Error during model training: {str(e)}")
            print("Please check the error and try again.")

    except Exception as e:
        print(f"Error during embedding extraction: {str(e)}")
        print("Please check the error and try again.")


if __name__ == "__main__":
    main()